{"name": "vibemate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^12.14.0", "geist": "^1.4.2", "lucide-react": "^0.511.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.3.0", "viem": "^2.0.0", "wagmi": "^2.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.4", "pino-pretty": "^13.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5"}}