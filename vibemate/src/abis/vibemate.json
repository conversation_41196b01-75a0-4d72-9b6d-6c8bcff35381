[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "enum VibeType", "name": "primaryVibe", "type": "uint8"}, {"indexed": false, "internalType": "enum Rarity", "name": "rarity", "type": "uint8"}], "name": "ProfileMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RewardsClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "token1", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "token2", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "compatibilityScore", "type": "uint256"}], "name": "SpicyMatch", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "newMessage", "type": "string"}], "name": "VibeUpdated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_myTokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_theirTokenId", "type": "uint256"}], "name": "findMatch", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_token1", "type": "uint256"}, {"internalType": "uint256", "name": "_token2", "type": "uint256"}], "name": "getCompatibility", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_matchId", "type": "uint256"}], "name": "getMatch", "outputs": [{"components": [{"internalType": "uint256", "name": "token1", "type": "uint256"}, {"internalType": "uint256", "name": "token2", "type": "uint256"}, {"internalType": "uint256", "name": "compatibilityScore", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "rewardPool", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct Match", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "getMyMatches", "outputs": [{"internalType": "uint256[]", "name": "matchIds", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "getProfile", "outputs": [{"components": [{"internalType": "enum VibeType", "name": "primaryVibe", "type": "uint8"}, {"internalType": "enum VibeType", "name": "secondaryVibe", "type": "uint8"}, {"internalType": "enum Rarity", "name": "rarity", "type": "uint8"}, {"internalType": "uint256", "name": "attractionLevel", "type": "uint256"}, {"internalType": "uint256", "name": "mintTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "totalMatches", "type": "uint256"}, {"internalType": "uint256", "name": "rewardsClaimed", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "string", "name": "customMessage", "type": "string"}], "internalType": "struct SexyProfile", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "matches", "outputs": [{"internalType": "uint256", "name": "token1", "type": "uint256"}, {"internalType": "uint256", "name": "token2", "type": "uint256"}, {"internalType": "uint256", "name": "compatibilityScore", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "rewardPool", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum VibeType", "name": "_primaryVibe", "type": "uint8"}, {"internalType": "enum VibeType", "name": "_secondaryVibe", "type": "uint8"}, {"internalType": "string", "name": "_customMessage", "type": "string"}], "name": "mintSexyProfile", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "profiles", "outputs": [{"internalType": "enum VibeType", "name": "primaryVibe", "type": "uint8"}, {"internalType": "enum VibeType", "name": "secondaryVibe", "type": "uint8"}, {"internalType": "enum Rarity", "name": "rarity", "type": "uint8"}, {"internalType": "uint256", "name": "attractionLevel", "type": "uint256"}, {"internalType": "uint256", "name": "mintTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "totalMatches", "type": "uint256"}, {"internalType": "uint256", "name": "rewardsClaimed", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "string", "name": "customMessage", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPool", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "string", "name": "_newMessage", "type": "string"}], "name": "updateVibeMessage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "userTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]