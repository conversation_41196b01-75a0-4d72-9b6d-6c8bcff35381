import { http, createConfig } from 'wagmi'
import { mainnet, sepolia } from 'wagmi/chains'
import { injected, metaMask, walletConnect, coinbaseWallet } from 'wagmi/connectors'
import { defineChain } from 'viem'

// Define Core testnet chain
export const coreTestnet = defineChain({
  id: 1115,
  name: 'Core Blockchain Testnet',
  nativeCurrency: {
    decimals: 18,
    name: 'tCORE',
    symbol: 'tCORE',
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.test2.btcs.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Core Scan',
      url: 'https://scan.test2.btcs.network',
    },
  },
  testnet: true,
})

// Use Core testnet for development, mainnet for production
const activeChain = process.env.NODE_ENV === 'production' ? mainnet : coreTestnet;

// Replace with your WalletConnect project ID
const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'YOUR_WALLETCONNECT_PROJECT_ID';

// Vibemate contract address
export const CONTRACT_ADDRESS = '******************************************';

// Create Wagmi config
export const config = createConfig({
  chains: [coreTestnet, sepolia, mainnet],
  connectors: [
    injected(),
    metaMask(),
    coinbaseWallet({
      appName: 'VibeMate',
      appLogoUrl: 'https://vibemate.app/logo.png',
    }),
    walletConnect({
      projectId,
      metadata: {
        name: 'VibeMate',
        description: 'Find Your Perfect Match on the Blockchain',
        url: 'https://vibemate.app',
        icons: ['https://vibemate.app/logo.png']
      }
    }),
  ],
  transports: {
    [coreTestnet.id]: http(),
    [sepolia.id]: http(),
    [mainnet.id]: http(),
  },
});