import { http, createConfig } from 'wagmi'
import { define<PERSON>hain } from 'viem'
import { injected, metaMask, walletConnect, coinbaseWallet } from 'wagmi/connectors'

// Define Core testnet 2 chain
export const coreTestnet = defineChain({
  id: 1114,
  name: 'Core Testnet',
  nativeCurrency: {
    name: 'Core',
    symbol: 'tCORE',
    decimals: 18
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.test2.btcs.network/']
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON> Scan',
      url: 'https://scan.test2.btcs.network/'
    },
  },
  testnet: true,
})

// Replace with your WalletConnect project ID
const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'YOUR_WALLETCONNECT_PROJECT_ID';

// Vibemate contract address
export const CONTRACT_ADDRESS = '******************************************';

// Create Wagmi config
export const config = createConfig({
  chains: [coreTestnet],
  connectors: [
    injected(),
    metaMask(),
    coinbaseWallet({
      appName: 'VibeMate',
      appLogoUrl: '/hero-image.png',
    }),
    walletConnect({
      projectId,
      metadata: {
        name: 'VibeMate',
        description: 'Find Your Perfect Match on the Blockchain',
        url: 'https://vibemate.app',
        icons: ['/hero-image.png']
      },
      showQrModal: true,
    }),
  ],
  transports: {
    [coreTestnet.id]: http('https://rpc.test2.btcs.network/'),
  },
});