@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  min-height: 100vh;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
  
  h1 {
    @apply text-4xl md:text-5xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
}

@layer components {
  .btn-primary {
    @apply bg-purple-600 text-white hover:bg-purple-700 px-4 py-2 rounded-md transition-colors;
  }
  
  .btn-secondary {
    @apply bg-transparent border border-purple-600 text-purple-600 hover:bg-purple-50 px-4 py-2 rounded-md transition-colors;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-r from-purple-700 to-pink-600;
  }
  
  .card {
    @apply bg-white p-6 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow;
  }
  
  .container-custom {
    @apply max-w-6xl mx-auto px-4 sm:px-6;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c084fc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse-subtle {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}

/* Custom hover effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-glow:hover {
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
}
