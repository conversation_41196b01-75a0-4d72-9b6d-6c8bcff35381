'use client'

import { useAccount, useConnect, useDisconnect } from 'wagmi'
import { injected, metaMask, walletConnect, coinbaseWallet } from 'wagmi/connectors'
import { useCallback } from 'react'

/**
 * Custom hook for managing wallet connections
 * Provides methods to connect to different wallet types and manage connection state
 */
export function useWalletConnection() {
  const { address, isConnected, connector } = useAccount()
  const { connect, isPending, error } = useConnect()
  const { disconnect } = useDisconnect()

  // Get WalletConnect project ID from environment
  const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'YOUR_WALLETCONNECT_PROJECT_ID'

  // Connect to injected wallet (browser extension wallets)
  const connectInjected = useCallback(async () => {
    try {
      await connect({ connector: injected() })
    } catch (error) {
      console.error('Failed to connect to injected wallet:', error)
      throw error
    }
  }, [connect])

  // Connect to MetaMask specifically
  const connectMetaMask = useCallback(async () => {
    try {
      await connect({ 
        connector: metaMask({
          dappMetadata: {
            name: 'VibeMate',
            url: 'https://vibemate.app',
            iconUrl: 'https://vibemate.app/logo.png',
          }
        })
      })
    } catch (error) {
      console.error('Failed to connect to MetaMask:', error)
      throw error
    }
  }, [connect])

  // Connect to Coinbase Wallet
  const connectCoinbaseWallet = useCallback(async () => {
    try {
      await connect({ 
        connector: coinbaseWallet({
          appName: 'VibeMate',
          appLogoUrl: 'https://vibemate.app/logo.png',
        })
      })
    } catch (error) {
      console.error('Failed to connect to Coinbase Wallet:', error)
      throw error
    }
  }, [connect])

  // Connect via WalletConnect
  const connectWalletConnect = useCallback(async () => {
    try {
      await connect({ 
        connector: walletConnect({
          projectId,
          metadata: {
            name: 'VibeMate',
            description: 'Find Your Perfect Match on the Blockchain',
            url: 'https://vibemate.app',
            icons: ['https://vibemate.app/logo.png']
          }
        })
      })
    } catch (error) {
      console.error('Failed to connect via WalletConnect:', error)
      throw error
    }
  }, [connect, projectId])

  return {
    // Connection state
    isConnected,
    address,
    connector,
    isPending,
    error,
    
    // Connection methods
    connectInjected,
    connectMetaMask,
    connectCoinbaseWallet,
    connectWalletConnect,
    disconnect,
  }
}
