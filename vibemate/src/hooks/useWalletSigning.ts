'use client'

import { useState, useCallback } from 'react'
import { useSignMessage, useAccount } from 'wagmi'

/**
 * Custom hook for wallet signing and verification
 * Handles message signing for wallet ownership verification
 */
export function useWalletSigning() {
  const { address } = useAccount()
  const { signMessageAsync } = useSignMessage()
  
  const [isVerifying, setIsVerifying] = useState(false)
  const [isVerified, setIsVerified] = useState(false)
  const [verificationError, setVerificationError] = useState<string | null>(null)

  // Generate a verification message
  const generateVerificationMessage = useCallback((walletAddress: string) => {
    const timestamp = Date.now()
    return `VibeMate Wallet Verification\n\nPlease sign this message to verify your wallet ownership.\n\nWallet: ${walletAddress}\nTimestamp: ${timestamp}\n\nThis signature will not trigger a blockchain transaction or cost any gas fees.`
  }, [])

  // Verify wallet ownership by signing a message
  const verifyWalletOwnership = useCallback(async (walletAddress: string) => {
    if (!walletAddress) {
      throw new Error('No wallet address provided')
    }

    setIsVerifying(true)
    setVerificationError(null)

    try {
      const message = generateVerificationMessage(walletAddress)
      const signature = await signMessageAsync({ message })
      
      // In a real application, you would send this signature to your backend
      // for verification and storage. For now, we'll just mark as verified.
      console.log('Signature generated:', signature)
      
      // Store verification status in localStorage (in production, use your backend)
      localStorage.setItem(`wallet_verified_${walletAddress}`, 'true')
      localStorage.setItem(`wallet_signature_${walletAddress}`, signature)
      
      setIsVerified(true)
      return true
    } catch (error: any) {
      console.error('Error verifying wallet ownership:', error)
      
      if (error.message?.includes('User rejected')) {
        setVerificationError('Signature request was rejected. Please approve the signature to verify your wallet.')
      } else if (error.message?.includes('User denied')) {
        setVerificationError('Signature request was denied. Please approve the signature to verify your wallet.')
      } else {
        setVerificationError(error.message || 'Failed to verify wallet ownership. Please try again.')
      }
      
      return false
    } finally {
      setIsVerifying(false)
    }
  }, [signMessageAsync, generateVerificationMessage])

  // Check if a wallet is already verified
  const checkWalletVerification = useCallback(async (walletAddress: string) => {
    if (!walletAddress) return false
    
    try {
      const verified = localStorage.getItem(`wallet_verified_${walletAddress}`)
      const isWalletVerified = verified === 'true'
      setIsVerified(isWalletVerified)
      return isWalletVerified
    } catch (error) {
      console.error('Error checking wallet verification:', error)
      return false
    }
  }, [])

  // Clear verification status (for logout/disconnect)
  const clearVerification = useCallback(() => {
    setIsVerified(false)
    setVerificationError(null)
    setIsVerifying(false)
  }, [])

  return {
    isVerifying,
    isVerified,
    verificationError,
    verifyWalletOwnership,
    checkWalletVerification,
    clearVerification,
  }
}
