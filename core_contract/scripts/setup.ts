import * as fs from 'fs';
import * as path from 'path';

// Define the content for the .env file
const envContent = `# Your wallet private key - keep this secret
PRIVATE_KEY=

# Core Testnet Configuration
CORE_TESTNET_RPC_URL=https://rpc.test2.btcs.network/
CORE_TESTNET_CHAIN_ID=1114
CORE_TESTNET_EXPLORER=https://scan.test2.btcs.network/
`;

// Get the path to the .env file
const envPath = path.join(__dirname, '..', '.env');

// Check if .env file exists
const envExists = fs.existsSync(envPath);

// Create or check the .env file
if (!envExists) {
  // Create the .env file with the content
  fs.writeFileSync(envPath, envContent);
  console.log('.env file has been created!');
} else {
  console.log('.env file already exists.');
}

// Instructions for the user
console.log('\n=== SETUP INSTRUCTIONS ===');
console.log('1. Open the .env file located in the root directory of this project.');
console.log('2. Add your private key to the PRIVATE_KEY field (without 0x prefix).');
console.log('3. Save the file.');
console.log('\nIMPORTANT: Keep your private key secure and never commit it to a repository.');
console.log('\nAfter setting up your .env file, you can deploy the contract with:');
console.log('npx hardhat run scripts/deploy.ts --network coreTestnet');
console.log('\nRefer to Core blockchain docs for more details:');
console.log('https://docs.coredao.org/docs');

// Exit successfully
process.exit(0); 