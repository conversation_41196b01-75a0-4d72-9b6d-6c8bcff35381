import { ethers } from "hardhat";

async function main() {
  console.log("Deploying SexyVibes contract to Core Testnet...");

  // Get the contract factory
  const SexyVibes = await ethers.getContractFactory("SexyVibes");

  // Deploy the contract
  const sexyVibes = await SexyVibes.deploy();
  await sexyVibes.waitForDeployment();

  // Get the deployed contract address
  const deployedAddress = await sexyVibes.getAddress();

  console.log(`SexyVibes deployed to ${deployedAddress}`);
  console.log(`Verify contract: npx hardhat verify --network coreTestnet ${deployedAddress}`);
}

// Execute the deployment
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
}); 