# SexyVibes NFT Contract

This project contains the SexyVibes NFT contract - the hottest NFT dating platform on the blockchain.

## Core Testnet Deployment

Follow these steps to deploy the contract to Core Testnet:

1. **Setup Environment**

   Run the setup script to create your .env file:
   ```shell
   npm run setup
   ```

2. **Add Your Private Key**

   Edit the `.env` file and add your wallet private key:
   ```
   PRIVATE_KEY=your_private_key_here_without_0x_prefix
   ```e

3. **Deploy the Contract**

   Run the deployment script:
   ```shell
   npm run deploy:testnet
   ```

4. **Verify the Contract (Optional)**

   Once deployed, verify your contract on the Core Testnet Explorer:
   ```shell
   npx hardhat verify --network coreTestnet YOUR_CONTRACT_ADDRESS
   ```

## Core Blockchain Resources

- [Core Blockchain Documentation](https://docs.coredao.org/docs)
- [Core Testnet Explorer](https://scan.test2.btcs.network/)
- [Core Testnet RPC URL](https://rpc.test2.btcs.network/)

## Development Commands

```shell
# Compile contracts
npx hardhat compile

# Run tests
npx hardhat test

# Start local node
npx hardhat node
```
