// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title SexyVibes - The Hottest NFT Dating Platform
 * @dev A spicy smart contract for minting attraction NFTs and finding matches
 * Features: Dynamic rarity, compatibility matching, reward pools, and steamy interactions
 */
contract SexyVibes is ERC721, Ownable, ReentrancyGuard {
    using Counters for Counters.Counter;
    
    Counters.Counter private _tokenIds;
    
    // Spicy enums for personality types
    enum VibeType { 
        MYSTERIOUS,    // 🌙 Dark and alluring
        PASSIONATE,    // 🔥 Fiery and intense  
        PLAYFUL,       // 💫 Fun and bubbly
        SOPHISTICATED, // 🍷 Classy and refined
        ADVENTUROUS,   // 🌊 Wild and free
        ROMANTIC       // 🌹 Sweet and loving
    }
    
    enum Rarity {
        COMMON,        // 60% chance
        RARE,          // 25% chance  
        EPIC,          // 12% chance
        LEGENDARY,     // 2.5% chance
        MYTHICAL       // 0.5% chance - Ultra rare unicorn vibes ✨
    }
    
    struct SexyProfile {
        VibeType primaryVibe;
        VibeType secondaryVibe;
        Rarity rarity;
        uint256 attractionLevel;     // 1-100, affects matching
        uint256 mintTimestamp;
        uint256 totalMatches;
        uint256 rewardsClaimed;
        bool isActive;
        string customMessage;       // Flirty tagline
    }
    
    struct Match {
        uint256 token1;
        uint256 token2;
        uint256 compatibilityScore; // 1-100
        uint256 timestamp;
        uint256 rewardPool;
        bool isActive;
    }
    
    // Storage
    mapping(uint256 => SexyProfile) public profiles;
    mapping(uint256 => Match) public matches;
    mapping(address => uint256[]) public userTokens;
    mapping(uint256 => mapping(uint256 => bool)) public hasMatched;
    
    uint256 public matchCounter;
    uint256 public constant MINT_PRICE = 0.01 ether;
    uint256 public constant MATCH_REWARD = 0.005 ether;
    uint256 public rewardPool;
    
    // Events for the hottest blockchain gossip
    event ProfileMinted(uint256 indexed tokenId, address indexed owner, VibeType primaryVibe, Rarity rarity);
    event SpicyMatch(uint256 indexed token1, uint256 indexed token2, uint256 compatibilityScore);
    event RewardsClaimed(address indexed user, uint256 amount);
    event VibeUpdated(uint256 indexed tokenId, string newMessage);
    
    constructor() ERC721("SexyVibes", "SEXY") Ownable() {}
    
    /**
     * @dev Mint your sexy NFT profile - Let the games begin! 🔥
     */
    function mintSexyProfile(
        VibeType _primaryVibe,
        VibeType _secondaryVibe,
        string memory _customMessage
    ) external payable nonReentrant {
        require(msg.value >= MINT_PRICE, "Not enough ETH to be this sexy");
        require(bytes(_customMessage).length <= 100, "Keep it short and spicy");
        
        _tokenIds.increment();
        uint256 newTokenId = _tokenIds.current();
        
        // Generate sexy stats with some randomness
        uint256 seed = uint256(keccak256(abi.encodePacked(
            block.timestamp,
            block.prevrandao,
            msg.sender,
            newTokenId
        )));
        
        Rarity rarity = _generateRarity(seed);
        uint256 attractionLevel = _generateAttractionLevel(seed, rarity);
        
        profiles[newTokenId] = SexyProfile({
            primaryVibe: _primaryVibe,
            secondaryVibe: _secondaryVibe,
            rarity: rarity,
            attractionLevel: attractionLevel,
            mintTimestamp: block.timestamp,
            totalMatches: 0,
            rewardsClaimed: 0,
            isActive: true,
            customMessage: _customMessage
        });
        
        userTokens[msg.sender].push(newTokenId);
        rewardPool += msg.value / 2; // Half goes to reward pool
        
        _safeMint(msg.sender, newTokenId);
        
        emit ProfileMinted(newTokenId, msg.sender, _primaryVibe, rarity);
    }
    
    /**
     * @dev Find your perfect match and earn rewards! 💕
     */
    function findMatch(uint256 _myTokenId, uint256 _theirTokenId) external nonReentrant {
        require(ownerOf(_myTokenId) == msg.sender, "Not your sexy profile");
        require(_myTokenId != _theirTokenId, "Can't match with yourself, narcissist");
        require(!hasMatched[_myTokenId][_theirTokenId], "Already matched with this hottie");
        require(profiles[_myTokenId].isActive && profiles[_theirTokenId].isActive, "Inactive profiles can't get spicy");
        
        uint256 compatibility = _calculateCompatibility(_myTokenId, _theirTokenId);
        
        if (compatibility >= 70) { // Hot match threshold! 🔥
            matchCounter++;
            
            matches[matchCounter] = Match({
                token1: _myTokenId,
                token2: _theirTokenId,
                compatibilityScore: compatibility,
                timestamp: block.timestamp,
                rewardPool: MATCH_REWARD,
                isActive: true
            });
            
            // Update match counts
            profiles[_myTokenId].totalMatches++;
            profiles[_theirTokenId].totalMatches++;
            
            // Mark as matched
            hasMatched[_myTokenId][_theirTokenId] = true;
            hasMatched[_theirTokenId][_myTokenId] = true;
            
            // Distribute rewards to both parties
            address owner1 = ownerOf(_myTokenId);
            address owner2 = ownerOf(_theirTokenId);
            
            if (rewardPool >= MATCH_REWARD * 2) {
                rewardPool -= MATCH_REWARD * 2;
                
                payable(owner1).transfer(MATCH_REWARD);
                payable(owner2).transfer(MATCH_REWARD);
                
                profiles[_myTokenId].rewardsClaimed += MATCH_REWARD;
                profiles[_theirTokenId].rewardsClaimed += MATCH_REWARD;
            }
            
            emit SpicyMatch(_myTokenId, _theirTokenId, compatibility);
        }
    }
    
    /**
     * @dev Update your flirty message
     */
    function updateVibeMessage(uint256 _tokenId, string memory _newMessage) external {
        require(ownerOf(_tokenId) == msg.sender, "Not your profile to update");
        require(bytes(_newMessage).length <= 100, "Keep it concise and sexy");
        
        profiles[_tokenId].customMessage = _newMessage;
        emit VibeUpdated(_tokenId, _newMessage);
    }
    
    /**
     * @dev Get compatibility score between two profiles
     */
    function getCompatibility(uint256 _token1, uint256 _token2) external view returns (uint256) {
        return _calculateCompatibility(_token1, _token2);
    }
    
    /**
     * @dev Get all matches for a token
     */
    function getMyMatches(uint256 _tokenId) external view returns (uint256[] memory matchIds) {
        uint256 count = 0;
        for (uint256 i = 1; i <= matchCounter; i++) {
            if ((matches[i].token1 == _tokenId || matches[i].token2 == _tokenId) && matches[i].isActive) {
                count++;
            }
        }
        
        matchIds = new uint256[](count);
        uint256 index = 0;
        for (uint256 i = 1; i <= matchCounter; i++) {
            if ((matches[i].token1 == _tokenId || matches[i].token2 == _tokenId) && matches[i].isActive) {
                matchIds[index] = i;
                index++;
            }
        }
    }
    
    // Internal functions
    
    function _generateRarity(uint256 _seed) internal pure returns (Rarity) {
        uint256 rand = _seed % 1000;
        if (rand < 5) return Rarity.MYTHICAL;      // 0.5%
        if (rand < 30) return Rarity.LEGENDARY;    // 2.5%
        if (rand < 150) return Rarity.EPIC;        // 12%
        if (rand < 400) return Rarity.RARE;        // 25%
        return Rarity.COMMON;                      // 60%
    }
    
    function _generateAttractionLevel(uint256 _seed, Rarity _rarity) internal pure returns (uint256) {
        uint256 base = (_seed % 41) + 50; // 50-90 base
        
        // Rarity bonus
        if (_rarity == Rarity.MYTHICAL) base += 10;
        else if (_rarity == Rarity.LEGENDARY) base += 8;
        else if (_rarity == Rarity.EPIC) base += 6;
        else if (_rarity == Rarity.RARE) base += 4;
        
        return base > 100 ? 100 : base;
    }
    
    function _calculateCompatibility(uint256 _token1, uint256 _token2) internal view returns (uint256) {
        SexyProfile memory profile1 = profiles[_token1];
        SexyProfile memory profile2 = profiles[_token2];
        
        uint256 score = 0;
        
        // Vibe compatibility (40 points max)
        if (profile1.primaryVibe == profile2.primaryVibe || 
            profile1.primaryVibe == profile2.secondaryVibe ||
            profile1.secondaryVibe == profile2.primaryVibe) {
            score += 20;
        }
        if (profile1.secondaryVibe == profile2.secondaryVibe) {
            score += 20;
        }
        
        // Attraction level compatibility (30 points max)
        uint256 attractionDiff = profile1.attractionLevel > profile2.attractionLevel ? 
            profile1.attractionLevel - profile2.attractionLevel : 
            profile2.attractionLevel - profile1.attractionLevel;
        score += (30 * (100 - attractionDiff)) / 100;
        
        // Rarity bonus (20 points max)
        if (profile1.rarity == profile2.rarity) {
            score += 15;
        }
        if (uint256(profile1.rarity) + uint256(profile2.rarity) >= 6) { // Both rare+
            score += 5;
        }
        
        // Time bonus for active profiles (10 points max)
        uint256 avgAge = (block.timestamp - profile1.mintTimestamp + block.timestamp - profile2.mintTimestamp) / 2;
        if (avgAge < 7 days) score += 10;
        else if (avgAge < 30 days) score += 5;
        
        return score > 100 ? 100 : score;
    }
    
    // Owner functions
    function withdrawFunds() external onlyOwner {
        uint256 balance = address(this).balance - rewardPool;
        require(balance > 0, "No funds to withdraw");
        payable(owner()).transfer(balance);
    }
    
    function emergencyWithdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
    
    // View functions
    function totalSupply() external view returns (uint256) {
        return _tokenIds.current();
    }
    
    function getProfile(uint256 _tokenId) external view returns (SexyProfile memory) {
        return profiles[_tokenId];
    }
    
    function getMatch(uint256 _matchId) external view returns (Match memory) {
        return matches[_matchId];
    }
}