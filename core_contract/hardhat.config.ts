import { HardhatUserConfig } from "hardhat/config";
import "@nomicfoundation/hardhat-toolbox";
import * as dotenv from "dotenv";

dotenv.config();

// Get private key from environment variables or use a default value for development
const PRIVATE_KEY = process.env.PRIVATE_KEY || "0x0000000000000000000000000000000000000000000000000000000000000000";

const config: HardhatUserConfig = {
  solidity: "0.8.28",
  networks: {
    coreTestnet: {
      url: process.env.CORE_TESTNET_RPC_URL || "https://rpc.test2.btcs.network/",
      chainId: parseInt(process.env.CORE_TESTNET_CHAIN_ID || "1114"),
      accounts: [PRIVATE_KEY],
    },
  },
  etherscan: {
    apiKey: {
      coreTestnet: "no-api-key-needed"
    },
    customChains: [
      {
        network: "coreTestnet",
        chainId: 1114,
        urls: {
          apiURL: "https://scan.test2.btcs.network/api",
          browserURL: "https://scan.test2.btcs.network/"
        }
      }
    ]
  }
};

export default config;
