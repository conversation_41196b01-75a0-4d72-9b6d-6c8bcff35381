{"name": "core_contract", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "setup": "npx hardhat run scripts/setup.ts", "deploy:testnet": "npx hardhat run scripts/deploy.ts --network coreTestnet", "verify:testnet": "echo 'Run: npx hardhat verify --network coreTestnet CONTRACT_ADDRESS'"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^5.0.0", "hardhat": "^2.24.0"}, "dependencies": {"@openzeppelin/contracts": "^4.9.3", "dotenv": "^16.4.5"}}